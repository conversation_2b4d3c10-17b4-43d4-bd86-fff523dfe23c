// Simple test script to verify submissions endpoint
const fetch = require('node-fetch');

async function testSubmissionsEndpoint() {
  try {
    console.log('Testing submissions endpoint...');
    
    // Test the public submissions endpoint
    const response = await fetch('http://localhost:3001/submissions');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    console.log('✅ Submissions endpoint is working!');
    console.log('Response structure:', {
      hasSubmittedArtwork: !!data.submittedArtwork,
      drawingsCount: data.submittedArtwork?.drawings?.length || 0,
      videosCount: data.submittedArtwork?.videos?.length || 0
    });
    
    // Check if the structure is correct
    if (data.submittedArtwork && 
        Array.isArray(data.submittedArtwork.drawings) && 
        Array.isArray(data.submittedArtwork.videos)) {
      console.log('✅ Data structure is correct!');
      console.log(`Found ${data.submittedArtwork.drawings.length} drawings and ${data.submittedArtwork.videos.length} videos`);
    } else {
      console.log('❌ Data structure is incorrect');
      console.log('Expected: { submittedArtwork: { drawings: [], videos: [] } }');
      console.log('Received:', JSON.stringify(data, null, 2));
    }
    
  } catch (error) {
    console.log('❌ Error testing submissions endpoint:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the backend server is running on port 3001');
    }
  }
}

// Run the test
testSubmissionsEndpoint();
