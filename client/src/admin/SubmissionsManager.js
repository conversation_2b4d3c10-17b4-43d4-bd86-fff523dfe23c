import React, { useState, useEffect, useCallback } from 'react';
import '../styles.css';

function SubmissionsManager({ url, csrfToken, fetchCsrfToken }) {
  const [submissions, setSubmissions] = useState({ listeningToPickleBoy: [], drawings: [], videos: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('listeningToPickleBoy'); // 'listeningToPickleBoy', 'drawings' or 'videos'
  const [newSubmission, setNewSubmission] = useState({
    title: '',
    desc: '',
    name: '',
    file: null,
    rotate: false
  });
  const [previewUrl, setPreviewUrl] = useState(null);

  // Helper function to detect file type from filename
  const getFileType = (filename) => {
    if (!filename) return 'unknown';
    const extension = filename.toLowerCase().split('.').pop();

    const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac'];
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];

    if (audioExtensions.includes(extension)) return 'audio';
    if (imageExtensions.includes(extension)) return 'image';
    if (videoExtensions.includes(extension)) return 'video';

    return 'unknown';
  };

  // Wrap fetchSubmissions in useCallback
  const fetchSubmissions = useCallback(async () => {
    try {
      setError(null); // Clear previous errors
      setLoading(true);
      // Construct the full API URL for admin submissions endpoint
      const apiUrl = url.endsWith('/') ? `${url}api/admin/submissions` : `${url}/api/admin/submissions`;
      const response = await fetch(apiUrl, {
        method: 'GET',
        credentials: 'include' // Include cookies in the request
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch submissions: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      // Check if data.submittedArtwork is valid before setting state
      if (data && typeof data.submittedArtwork === 'object' && data.submittedArtwork !== null &&
        Array.isArray(data.submittedArtwork.listeningToPickleBoy) &&
        Array.isArray(data.submittedArtwork.drawings) && Array.isArray(data.submittedArtwork.videos)) {
        setSubmissions(data.submittedArtwork);
      } else {
        console.warn('Received unexpected data structure for submissions:', data);
        setSubmissions({ listeningToPickleBoy: [], drawings: [], videos: [] }); // Set to default empty state
      }
      setLoading(false);
    } catch (err) {
      console.error('Error fetching submissions:', err);
      setError(err.message);
      setLoading(false);
    }
  }, [url]);

  // Fetch submissions when component mounts
  useEffect(() => {
    fetchSubmissions();
  }, [fetchSubmissions]);

  // Handle file input change
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type based on active tab
      const isValidFileType = () => {
        if (activeTab === 'listeningToPickleBoy') {
          return file.type.startsWith('image/');
        } else if (activeTab === 'drawings') {
          return file.type.startsWith('image/');
        } else if (activeTab === 'videos') {
          return file.type.startsWith('video/');
        }
        return false;
      };

      if (!isValidFileType()) {
        const expectedType = activeTab === 'listeningToPickleBoy' ? 'image' :
          activeTab === 'drawings' ? 'image' : 'video';
        alert(`Please select a valid ${expectedType} file for the ${activeTab} category.`);
        e.target.value = ''; // Clear the file input
        return;
      }

      setNewSubmission({ ...newSubmission, file });

      // Create a preview URL for the file
      const fileReader = new FileReader();
      fileReader.onload = () => {
        setPreviewUrl(fileReader.result);
      };
      fileReader.readAsDataURL(file);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewSubmission({
      ...newSubmission,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!newSubmission.title.trim() || !newSubmission.file) {
      alert('Please provide a title and upload a file');
      return;
    }

    // Double-check file type before submission
    const isValidFileType = () => {
      if (activeTab === 'listeningToPickleBoy') {
        return newSubmission.file.type.startsWith('image/');
      } else if (activeTab === 'drawings') {
        return newSubmission.file.type.startsWith('image/');
      } else if (activeTab === 'videos') {
        return newSubmission.file.type.startsWith('video/');
      }
      return false;
    };

    if (!isValidFileType()) {
      const expectedType = activeTab === 'listeningToPickleBoy' ? 'image' :
        activeTab === 'drawings' ? 'image' : 'video';
      alert(`Invalid file type. Please select a valid ${expectedType} file for the ${activeTab} category.`);
      return;
    }

    setLoading(true);

    try {
      setError(null); // Clear previous errors

      // Get CSRF token if not available
      let token = csrfToken;
      if (!token && fetchCsrfToken) {
        token = await fetchCsrfToken();
        if (!token) {
          throw new Error('Failed to get CSRF token');
        }
      }

      const formData = new FormData();
      formData.append('title', newSubmission.title);
      formData.append('desc', newSubmission.desc);
      formData.append('name', newSubmission.name);
      formData.append('rotate', newSubmission.rotate);
      formData.append('type', activeTab === 'listeningToPickleBoy' ? 'listeningToPickleBoy' : (activeTab === 'drawings' ? 'drawing' : 'video'));
      formData.append('file', newSubmission.file);

      // Construct the full API URL
      const apiUrl = url.endsWith('/') ? `${url}api/admin/submissions` : `${url}/api/admin/submissions`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'CSRF-Token': token, // Include CSRF token
        },
        credentials: 'include', // Include cookies in the request
        body: formData
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to add submission: ${response.status} ${response.statusText} - ${errorText}`);
      }

      // Refresh submissions list
      fetchSubmissions();

      // Reset form
      setNewSubmission({
        title: '',
        desc: '',
        name: '',
        file: null,
        rotate: false
      });
      setPreviewUrl(null);

      alert('Submission added successfully!');
    } catch (err) {
      console.error('Error adding submission:', err);
      setError(err.message);
      alert(`Error adding submission: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle deleting a submission
  const handleDelete = async (id, type) => {
    if (!window.confirm('Are you sure you want to delete this submission?')) {
      return;
    }

    setLoading(true);

    try {
      setError(null); // Clear previous errors

      // Get CSRF token if not available
      let token = csrfToken;
      if (!token && fetchCsrfToken) {
        token = await fetchCsrfToken();
        if (!token) {
          throw new Error('Failed to get CSRF token');
        }
      }

      // Construct the full API URL
      const apiUrl = url.endsWith('/') ? `${url}api/admin/submissions/${id}` : `${url}/api/admin/submissions/${id}`;

      const response = await fetch(apiUrl, {
        method: 'DELETE',
        credentials: 'include', // Include cookies in the request
        headers: {
          'Content-Type': 'application/json',
          'x-csrf-token': token, // Include CSRF token
        },
        body: JSON.stringify({ type })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete submission: ${response.status} ${response.statusText} - ${errorText}`);
      }

      // Refresh submissions list
      fetchSubmissions();

      alert('Submission deleted successfully!');
    } catch (err) {
      console.error('Error deleting submission:', err);
      setError(err.message);
      alert(`Error deleting submission: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !submissions.listeningToPickleBoy.length && !submissions.drawings.length && !submissions.videos.length) {
    return (
      <div className="loading-spinner">
        <div className="spinner"></div>
        <p>Loading submissions...</p>
      </div>
    );
  }

  if (error && !submissions.listeningToPickleBoy.length && !submissions.drawings.length && !submissions.videos.length) {
    return (
      <div className="error-message">
        <p>Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="faqs-section">
      <div className="section-header">
        <h2>Manage Submissions</h2>
        <div className="section-header-buttons">
          <button
            className={`tab-button ${activeTab === 'listeningToPickleBoy' ? 'active' : ''}`}
            onClick={() => setActiveTab('listeningToPickleBoy')}
          >
            Listening to PickleBoy
          </button>
          <button
            className={`tab-button ${activeTab === 'drawings' ? 'active' : ''}`}
            onClick={() => setActiveTab('drawings')}
          >
            Drawings
          </button>
          <button
            className={`tab-button ${activeTab === 'videos' ? 'active' : ''}`}
            onClick={() => setActiveTab('videos')}
          >
            Videos
          </button>
        </div>
      </div>

      <div className="create-form">
        <h3>Add New {activeTab === 'listeningToPickleBoy' ? 'Listening Submission' : (activeTab === 'drawings' ? 'Drawing' : 'Video')}</h3>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="title">Title:</label>
            <input
              type="text"
              id="title"
              name="title"
              value={newSubmission.title}
              onChange={handleInputChange}
              placeholder="Submission title"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="desc">Description (optional):</label>
            <textarea
              id="desc"
              name="desc"
              value={newSubmission.desc}
              onChange={handleInputChange}
              placeholder="Submission description"
              rows="3"
            />
          </div>

          <div className="form-group">
            <label htmlFor="name">Artist Name (optional):</label>
            <input
              type="text"
              id="name"
              name="name"
              value={newSubmission.name}
              onChange={handleInputChange}
              placeholder="Artist name"
            />
          </div>

          <div className="form-group">
            <label htmlFor="file">Upload {activeTab === 'listeningToPickleBoy' ? 'Image' : (activeTab === 'drawings' ? 'Image' : 'Video')}:</label>
            <input
              type="file"
              id="file"
              name="file"
              onChange={handleFileChange}
              accept={activeTab === 'listeningToPickleBoy' ? "image/jpeg,image/png,image/gif" : (activeTab === 'drawings' ? "image/jpeg,image/png,image/gif" : "video/mp4")}
              required
            />
          </div>

          {activeTab === 'drawings' && (
            <div className="form-group checkbox">
              <label htmlFor="rotate">
                <input
                  type="checkbox"
                  id="rotate"
                  name="rotate"
                  checked={newSubmission.rotate}
                  onChange={handleInputChange}
                />
                Rotate image in carousel
              </label>
            </div>
          )}

          {previewUrl && (
            <div className="preview">
              <h4>Preview:</h4>
              {activeTab === 'listeningToPickleBoy' ? (
                <img src={previewUrl} alt="Preview" style={{ maxWidth: '100%', maxHeight: '200px' }} />
              ) : activeTab === 'drawings' ? (
                <img src={previewUrl} alt="Preview" style={{ maxWidth: '100%', maxHeight: '200px' }} />
              ) : (
                <video controls style={{ maxWidth: '100%', maxHeight: '200px' }}>
                  <source src={previewUrl} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              )}
            </div>
          )}

          <button type="submit" className="admin-button save" disabled={loading}>
            {loading ? 'Adding...' : `Add ${activeTab === 'listeningToPickleBoy' ? 'Listening Submission' : (activeTab === 'drawings' ? 'Drawing' : 'Video')}`}
          </button>
        </form>
      </div>

      <div className="section-header">
        <h2>Current {activeTab === 'listeningToPickleBoy' ? 'Listening Submissions' : (activeTab === 'drawings' ? 'Drawings' : 'Videos')}</h2>
      </div>

      {activeTab === 'listeningToPickleBoy' && submissions.listeningToPickleBoy.length === 0 ? (
        <p className="empty-message">No listening submissions found.</p>
      ) : activeTab === 'drawings' && submissions.drawings.length === 0 ? (
        <p className="empty-message">No drawings found.</p>
      ) : activeTab === 'videos' && submissions.videos.length === 0 ? (
        <p className="empty-message">No videos found.</p>
      ) : (
        <div className="faqs-list">
          {activeTab === 'listeningToPickleBoy' ?
            submissions.listeningToPickleBoy.map((item) => {
              const fileType = getFileType(item.images[0]);
              return (
                <div key={item.id} className="faq-item">
                  <div className="faq-details">
                    <h4 className="faq-question">{item.title}</h4>
                    {fileType === 'image' ? (
                      <div className="submission-image">
                        <img
                          src={`${url}images/${item.images[0]}`}
                          alt={item.title}
                          style={{ maxWidth: '100%', maxHeight: '200px', borderRadius: '8px' }}
                        />
                      </div>
                    ) : fileType === 'audio' ? (
                      <div className="submission-audio">
                        <audio controls style={{ width: '100%' }}>
                          <source src={`${url}images/${item.images[0]}`} type="audio/mpeg" />
                          Your browser does not support the audio tag.
                        </audio>
                        <p style={{ color: 'orange', fontSize: '12px', marginTop: '5px' }}>
                          ⚠️ This audio file is in the wrong category. "Listening to PickleBoy" is for images only.
                        </p>
                      </div>
                    ) : fileType === 'video' ? (
                      <div className="submission-video">
                        <video controls style={{ maxWidth: '100%', maxHeight: '200px', borderRadius: '8px' }}>
                          <source src={`${url}images/${item.images[0]}`} type="video/mp4" />
                          Your browser does not support the video tag.
                        </video>
                        <p style={{ color: 'orange', fontSize: '12px', marginTop: '5px' }}>
                          ⚠️ This video file is in the wrong category. Consider moving it to "Videos".
                        </p>
                      </div>
                    ) : (
                      <div className="submission-unknown">
                        <p>Unknown file type: {item.images[0]}</p>
                      </div>
                    )}
                    {item.desc && <p className="faq-answer">{item.desc}</p>}
                    <div className="faq-info">
                      {item.name && <span>By: {item.name}</span>}
                    </div>
                  </div>
                  <div className="faq-action-buttons">
                    <button
                      className="admin-button delete"
                      onClick={() => handleDelete(item.id, 'listeningToPickleBoy')}
                      disabled={loading}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              );
            }) : activeTab === 'drawings' ?
              submissions.drawings.map((item) => (
                <div key={item.id} className="faq-item">
                  <div className="faq-details">
                    <h4 className="faq-question">{item.title}</h4>
                    <div className="submission-image">
                      <img
                        src={`${url}images/${item.images[0]}`}
                        alt={item.title}
                        style={{ maxWidth: '100%', maxHeight: '200px', borderRadius: '8px' }}
                      />
                    </div>
                    {item.desc && <p className="faq-answer">{item.desc}</p>}
                    <div className="faq-info">
                      {item.name && <span>By: {item.name}</span>}
                    </div>
                  </div>
                  <div className="faq-action-buttons">
                    <button
                      className="admin-button delete"
                      onClick={() => handleDelete(item.id, 'drawing')}
                      disabled={loading}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              )) :
              submissions.videos.map((item) => (
                <div key={item.id} className="faq-item">
                  <div className="faq-details">
                    <h4 className="faq-question">{item.title}</h4>
                    <div className="submission-video">
                      <video controls style={{ maxWidth: '100%', maxHeight: '200px', borderRadius: '8px' }}>
                        <source src={`${url}images/${item.images[0]}`} type="video/mp4" />
                        Your browser does not support the video tag.
                      </video>
                    </div>
                    {item.desc && <p className="faq-answer">{item.desc}</p>}
                    <div className="faq-info">
                      {item.name && <span>By: {item.name}</span>}
                    </div>
                  </div>
                  <div className="faq-action-buttons">
                    <button
                      className="admin-button delete"
                      onClick={() => handleDelete(item.id, 'video')}
                      disabled={loading}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))
          }
        </div>
      )}
    </div>
  );
}

export default SubmissionsManager;
