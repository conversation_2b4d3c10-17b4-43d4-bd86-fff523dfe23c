/* ====================================
   Admin Panel - Modern Redesign
   ==================================== */

/* Admin Container - High specificity to override existing styles */
.admin-container {
  min-height: 100vh !important;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f5e9 100%) !important;
  padding-bottom: 60px !important;
}

/* Admin Hero Section */
.admin-hero {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%) !important;
  color: white !important;
  padding: 60px 20px 40px !important;
  text-align: center !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

.admin-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSg0NSkiPjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjA1KSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXR0ZXJuKSIvPjwvc3ZnPg==');
  opacity: 0.6;
}

.admin-hero-content {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  z-index: 1;
}

.admin-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 15px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: -1px;
  line-height: 1.1;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.admin-status {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.2);
  padding: 10px 20px;
  border-radius: 8px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.login-status {
  font-weight: 600;
  font-size: 1rem;
  opacity: 0.9;
}

/* Admin Tabs */
.admin-tabs {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin: -20px auto 0;
  padding: 0 20px;
  position: relative;
  z-index: 2;
  max-width: 800px;
}

.tab-button {
  padding: 14px 28px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  border: none !important;
  border-radius: 25px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  color: #2E7D32 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  border: 2px solid transparent !important;
}

.tab-button:hover {
  background: rgba(255, 255, 255, 1) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
}

.tab-button.active {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%) !important;
  color: white !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 25px rgba(76, 175, 80, 0.3) !important;
}

/* Admin Content Container */
.admin-content-container {
  max-width: 1200px;
  margin: 40px auto 0;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 25px 30px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(76, 175, 80, 0.1);
}

.section-header h2 {
  margin: 0;
  color: #2E7D32;
  font-size: 1.8rem;
  font-weight: 700;
}

.section-header-buttons {
  display: flex;
  gap: 12px;
}

.admin-login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f5e9 100%);
}

/* Login Form Styling */
.admin-login-box {
  width: 450px;
  padding: 40px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.1);
}

.admin-login-box h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #2E7D32;
  font-size: 2rem;
  font-weight: 700;
}

.admin-login-error {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  color: #c62828;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 25px;
  text-align: center;
  border: 1px solid #ef5350;
  font-weight: 500;
}

.admin-login-field {
  margin-bottom: 25px;
}

.admin-login-field label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2E7D32;
  font-size: 1rem;
}

.admin-login-field input {
  width: 100%;
  padding: 15px 18px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fafafa;
}

.admin-login-field input:focus {
  outline: none;
  border-color: #4CAF50;
  background: white;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.admin-login-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.admin-login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.admin-login-button:disabled {
  background: #bdbdbd;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* FAQ Items and Lists */
.faqs-section {
  animation: fadeInUp 0.6s ease-out forwards;
}

.faqs-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.faq-item {
  background: white;
  border-radius: 15px;
  padding: 25px 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(76, 175, 80, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.faq-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.faq-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.faq-item:hover::before {
  opacity: 1;
}

.faq-details {
  margin-bottom: 20px;
}

.faq-question {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2E7D32;
  margin-bottom: 15px;
  line-height: 1.4;
}

.faq-answer {
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e9 100%);
  padding: 15px 20px;
  border-radius: 10px;
  margin: 15px 0;
  border-left: 4px solid #4CAF50;
  font-size: 1rem;
  line-height: 1.5;
}

.faq-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.9rem;
  color: #666;
}

.faq-info span {
  background: #f5f5f5;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
  border: 1px solid #e0e0e0;
}

.faq-action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* Create Form Styling */
.create-form {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(76, 175, 80, 0.1);
}

.create-form h3 {
  color: #2E7D32;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 25px;
  text-align: center;
}

/* Form Styling */
.create-form .form-group {
  margin-bottom: 20px;
}

.create-form .form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2E7D32;
  font-size: 1rem;
}

.create-form input,
.create-form textarea,
.create-form select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fafafa;
  font-family: inherit;
}

.create-form input:focus,
.create-form textarea:focus,
.create-form select:focus {
  outline: none;
  border-color: #4CAF50;
  background: white;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.create-form textarea {
  resize: vertical;
  min-height: 100px;
}

/* FAQ Answer Form */
.faq-answer-form {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.faq-answer-form .form-group {
  margin-bottom: 20px;
}

.faq-answer-form .form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.faq-answer-form textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fafafa;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
}

.faq-answer-form textarea:focus {
  outline: none;
  border-color: #4CAF50;
  background: white;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.faq-answer-form select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fafafa;
  font-family: inherit;
}

.faq-answer-form select:focus {
  outline: none;
  border-color: #4CAF50;
  background: white;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* Loading and Error States */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 15px;
  margin: 30px auto;
  max-width: 400px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-spinner p {
  color: #666;
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
}

.error-message {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  color: #c62828;
  padding: 25px 30px;
  border-radius: 15px;
  margin: 30px auto;
  max-width: 600px;
  border: 1px solid #ef5350;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.empty-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: white;
  border-radius: 15px;
  margin: 30px auto;
  max-width: 500px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  font-size: 1.1rem;
  font-weight: 500;
}

/* Debug Info */
.debug-info {
  background: white;
  border-radius: 15px;
  padding: 25px 30px;
  margin: 30px auto;
  max-width: 800px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(76, 175, 80, 0.1);
}

.debug-info h3 {
  color: #2E7D32;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.debug-info pre {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow-x: auto;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Confirm Delete Styling */
.confirm-delete-text {
  color: #d32f2f;
  font-weight: 600;
  font-size: 0.9rem;
  margin-right: 10px;
}

/* Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.admin-container {
  animation: fadeIn 0.6s ease-out forwards;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-title {
    font-size: 2rem;
    line-height: 1.2;
  }

  .admin-hero {
    padding: 30px 15px 25px;
  }

  .admin-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .admin-status {
    padding: 8px 16px;
    font-size: 14px;
  }

  .admin-tabs {
    flex-direction: column;
    gap: 8px;
    margin: -15px auto 0;
    max-width: 280px;
    padding: 0 15px;
  }

  .tab-button {
    padding: 14px 20px;
    font-size: 16px;
    min-height: 48px;
    /* Better touch target */
    border-radius: 8px;
    font-weight: 600;
  }

  .admin-content-container {
    margin: 25px auto 0;
    padding: 0 15px;
  }

  .section-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 20px 15px;
    margin-bottom: 20px;
  }

  .section-header h2 {
    font-size: 1.4rem;
    margin-bottom: 0;
  }

  .section-header-buttons {
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
  }

  .section-header-buttons button {
    min-height: 44px;
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 8px;
  }

  .faq-item {
    padding: 16px;
    margin-bottom: 15px;
    border-radius: 10px;
  }

  .faq-question {
    font-size: 1rem;
    line-height: 1.4;
    margin-bottom: 12px;
  }

  .faq-answer {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
  }

  .faq-action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .faq-action-buttons button {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 6px;
  }

  /* Admin login form improvements */
  .admin-login-container {
    padding: 20px 15px;
    margin: 20px auto;
    max-width: 100%;
  }

  .admin-login-form {
    padding: 25px 20px;
  }

  .admin-login-input {
    font-size: 16px;
    /* Prevents zoom on iOS */
    padding: 14px 16px;
    margin-bottom: 16px;
    border-radius: 8px;
  }

  .admin-login-button {
    min-height: 48px;
    font-size: 16px;
    padding: 14px;
    border-radius: 8px;
  }
}

/* Additional mobile improvements for very small screens */
@media (max-width: 480px) {
  .admin-title {
    font-size: 1.8rem;
  }

  .admin-hero {
    padding: 25px 10px 20px;
  }

  .admin-tabs {
    max-width: 100%;
    padding: 0 10px;
  }

  .tab-button {
    padding: 12px 16px;
    font-size: 15px;
  }

  .admin-content-container {
    padding: 0 10px;
  }

  .section-header {
    padding: 15px 10px;
  }

  .section-header h2 {
    font-size: 1.2rem;
  }

  .faq-item {
    padding: 12px;
  }

  .faq-question {
    font-size: 0.95rem;
  }

  .faq-answer {
    font-size: 13px;
  }

  .admin-login-form {
    padding: 20px 15px;
  }

  .admin-login-input {
    padding: 12px 14px;
  }

  .admin-login-button {
    padding: 12px;
    font-size: 15px;
  }
}

.create-form {
  padding: 20px;
}

/* Mobile form improvements */
@media (max-width: 768px) {
  .create-form {
    padding: 15px;
  }

  .create-form input,
  .create-form textarea,
  .create-form select {
    font-size: 16px;
    /* Prevents zoom on iOS */
    padding: 14px 16px;
    margin-bottom: 16px;
    border-radius: 8px;
    width: 100%;
    box-sizing: border-box;
  }

  .create-form button {
    min-height: 48px;
    padding: 14px 20px;
    font-size: 16px;
    border-radius: 8px;
    width: 100%;
    margin-top: 10px;
  }

  .create-form label {
    font-size: 16px;
    margin-bottom: 8px;
    display: block;
    font-weight: 500;
  }
}

.admin-login-box {
  width: 90%;
  max-width: 400px;
  padding: 30px 25px;
}

.admin-login-box h2 {
  font-size: 1.7rem;
}