import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import Modal from 'react-modal';

import 'bootstrap/dist/css/bootstrap.min.css';
import './styles.css';
import Logo from './images/logoNoBackground.png';

import Home from "./home";
import About from "./about";
import Contact from "./contact";
import Contest from './contest/contest';
import Faq from './faq';
import PurimContest2025 from './contest/purimContest2025';
import News from './News';
import DontQualify from './DontQualify';
import OfficialArtwork from "./OfficialArtwork";
import Signup from "./signup";
import Admin from "./admin/admintest";


Modal.setAppElement('#root');

export default function App() {
  const location = useLocation();
  const navigate = useNavigate();
  const [menuOpen, setMenuOpen] = useState(false);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 767) {
        setMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const showHeaderFooter = location.pathname !== '/dont-qualify';

  const inTesting = process.env.REACT_APP_IN_TESTING === 'true';
  const url = inTesting ? 'http://localhost:3001/' : 'https://pickle-boy-backend.onrender.com/';

  // Check if user is admin
  const isAdmin = () => {
    return localStorage.getItem('adminToken') !== null;
  };

  const [, setTab] = useState("home");
  const navbar = {
    home: "HOME",
    contact: "SUBSCRIBE",
    contest: "CONTEST",
    about: "ABOUT",
    faq: "FAQ",
    purimContest2025: "PURIM CONTEST",
    news: "NEWS",
  };

  // Add admin tab if user is admin
  if (isAdmin()) {
    navbar.admin = "ADMIN";
  }

  const moveTab = (val) => {
    switch (val) {
      case "ADMIN":
        // For development: Create a temporary admin token if one doesn't exist
        if (!localStorage.getItem('adminToken')) {
          localStorage.setItem('adminToken', 'temp-admin-token');
        }
        navigate('/admin');
        break;
      case "HOME":
        setTab("home");
        navigate('/');
        break;
      case "ABOUT":
        setTab("about");
        navigate('/about');
        break;
      case "SUBSCRIBE":
        setTab("contact");
        navigate('/subscribe');
        break;
      case "CONTEST":
        setTab("contest");
        navigate('/submissions');
        break;
      case "PURIM CONTEST":
        setTab("purimContest2025");
        navigate('/purim-contest');
        break;
      case "FAQ":
        setTab("faq");
        navigate('/faq');
        break;
      case "NEWS":
        setTab("news");
        navigate('/news');
        break;
      default:
        setTab("home");
        navigate('/');
    }
    setMenuOpen(false);
  };

  return (
    <HelmetProvider>
      <div className='header'>
        <img className='logo' alt='logo' src={Logo}></img>
        <button className="mobile-menu-toggle" onClick={toggleMenu}>
          <div className={`hamburger ${menuOpen ? 'open' : ''}`}>
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>

        <nav className={`navBar ${menuOpen ? 'menu-open' : ''}`}>
          <button className="close-menu" onClick={toggleMenu}></button>
          <ul>
            <li className="listItem" onClick={() => moveTab(navbar.home)}>
              HOME
            </li>
            <li className="listItem" onClick={() => moveTab(navbar.about)}>
              ABOUT
            </li>
            <li className="listItem" onClick={() => moveTab(navbar.news)}>
              NEWS
            </li>
            <li className="listItem" onClick={() => moveTab(navbar.contest)}>
              SUBMISSIONS
            </li>
            <li className="listItem" onClick={() => moveTab(navbar.purimContest2025)}>
              PURIM CONTEST
            </li>
            <li className="listItem" onClick={() => moveTab(navbar.faq)}>
              FAQ
            </li>
            <li className="listItem" onClick={() => moveTab(navbar.contact)}>
              SUBSCRIBE
            </li>
          </ul>
        </nav>
      </div>



      <div className="page">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
          <Route path="/submissions" element={<Contest url={url} />} />
          <Route path="/subscribe" element={<Contact />} />
          <Route path="/faq" element={<Faq url={url} />} />
          <Route path="/purim-contest" element={<PurimContest2025 url={url} />} />
          <Route path="/news" element={<News />} />
          <Route path="/dont-qualify" element={<DontQualify />} />
          <Route path="/pickleboy-artwork" element={<OfficialArtwork />} />
          <Route path="/signup" element={<Signup url={url} />} />
          <Route path="/admin" element={<Admin url={url} />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
        {showHeaderFooter && <footer className="site-footer"></footer>}
      </div>

      <div className='footer'></div>
    </HelmetProvider>
  );
}
