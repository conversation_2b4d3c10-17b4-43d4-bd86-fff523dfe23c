import React from 'react';
import { Helmet } from 'react-helmet-async';

const StructuredData = ({ type = 'website', data = {} }) => {
  const generateStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
    };

    switch (type) {
      case 'organization':
        return {
          ...baseData,
          "@type": "Organization",
          "@id": "https://pickle-boy.com/#organization",
          "name": "The Secret Adventures of Pickle Boy",
          "url": "https://pickle-boy.com/",
          "logo": {
            "@type": "ImageObject",
            "url": "https://pickle-boy.com/images/logoNoBackground.png",
            "width": 512,
            "height": 512
          },
          "sameAs": [
            "https://mostlymusic.com/products/yaakov-bee-the-secret-adventures-of-pickle-boy-season-1",
            "https://open.spotify.com/album/4zxhOTPflEFqOXzPT1mgep",
            "https://music.apple.com/us/album/the-secret-adventures-of-pickle-boy-season-1-episode/1762989929",
            "https://24six.app/app/music/collection/8910"
          ],
          ...data
        };

      case 'audioObject':
        return {
          ...baseData,
          "@type": "AudioObject",
          "name": data.name || "The Secret Adventures of Pickle Boy - Audio Series",
          "description": data.description || "An engaging audio series featuring Pickle Boy's secret adventures, perfect for family entertainment.",
          "creator": {
            "@type": "Person",
            "name": "Yaakov Bee"
          },
          "publisher": {
            "@id": "https://pickle-boy.com/#organization"
          },
          "genre": "Children's Audio Stories",
          "inLanguage": "en-US",
          "contentUrl": data.contentUrl || "https://pickle-boy.com/",
          "embedUrl": data.embedUrl || "https://pickle-boy.com/",
          ...data
        };

      case 'event':
        return {
          ...baseData,
          "@type": "Event",
          "name": data.name || "Pickle Boy Contest",
          "description": data.description || "Participate in The Secret Adventures of Pickle Boy contest",
          "startDate": data.startDate,
          "endDate": data.endDate,
          "eventStatus": "https://schema.org/EventScheduled",
          "eventAttendanceMode": "https://schema.org/OnlineEventAttendanceMode",
          "location": {
            "@type": "VirtualLocation",
            "url": "https://pickle-boy.com/"
          },
          "organizer": {
            "@id": "https://pickle-boy.com/#organization"
          },
          ...data
        };

      case 'faqPage':
        return {
          ...baseData,
          "@type": "FAQPage",
          "mainEntity": data.questions?.map(q => ({
            "@type": "Question",
            "name": q.question,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": q.answer
            }
          })) || [],
          ...data
        };


      case 'website':
      default:
        return {
          ...baseData,
          "@type": "WebSite",
          "@id": "https://pickle-boy.com/#website",
          "url": "https://pickle-boy.com/",
          "name": "The Secret Adventures of Pickle Boy",
          "description": "Join Pickle Boy on his secret adventures! Listen to episodes, participate in contests, submit artwork, and connect with fans of this popular audio series with over 4 million streams.",
          "publisher": {
            "@id": "https://pickle-boy.com/#organization"
          },
          "potentialAction": [
            {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": "https://pickle-boy.com/search?q={search_term_string}"
              },
              "query-input": "required name=search_term_string"
            }
          ],
          ...data
        };
    }
  };

  const structuredData = generateStructuredData();

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

export default StructuredData;
