# Next.js Recreation Prompt for Pickle Boy Website

## Project Overview
Create a Next.js application that replicates the functionality and appearance of the Pickle Boy website. This website is a React-based application with a Node.js/Express backend and MongoDB database. The site features contest submissions, voting, admin functionality, and various informational pages.

## Technical Requirements

### Frontend
- Use Next.js 14+ with App Router
- Implement responsive design matching the current site's appearance
- Use React hooks for state management
- Implement client-side form validation
- Create reusable components for common UI elements
- Implement proper error handling and loading states

### Backend
- Use Next.js API routes for backend functionality
- Implement MongoDB connection using Mongoose
- Set up proper authentication and authorization for admin features
- Implement security best practices (rate limiting, input validation, etc.)
- Create API endpoints for all required functionality

### Authentication
- Implement JWT-based authentication for admin access
- Store tokens securely in cookies with proper settings
- Include middleware for protected routes

## Key Features to Implement

### General
1. **Navigation**
   - Responsive navbar with mobile hamburger menu
   - Navigation links to all main sections

2. **Pages**
   - Home page with main content
   - About page with information
   - News page with updates
   - FAQ page with questions and answers
   - Contact/Subscribe page for user communication
   - Various contest pages

### Contest Functionality
1. **Submission Display**
   - Grid layout for displaying contest entries
   - Modal for viewing larger images
   - Categorization of submissions (drawings, videos, etc.)
   - Support for multiple images per submission

2. **Purim Contest**
   - Display of current year's costume entries
   - Display of previous year's entries
   - Voting functionality (one vote per user)
   - Confirmation of vote submission
   - Error handling for API failures and image loading

3. **Voting System**
   - Allow users to vote for one entry
   - Prevent multiple votes from the same user (using localStorage)
   - Submit votes to backend
   - Display confirmation after successful vote

### Admin Functionality
1. **Admin Authentication**
   - Login page with secure authentication
   - JWT token storage and verification

2. **Admin Dashboard**
   - Sidebar navigation for different admin functions
   - Vote results display with:
     - Bar chart visualization (using a charting library like recharts)
     - Tabular data showing votes and percentages
     - Summary statistics

## API Endpoints to Implement

1. **Contest Data**
   - `GET /api/publicPurimContest2025` - Get Purim contest entries
   - `POST /api/contestData` - Submit votes for contest entries

2. **Admin**
   - `POST /api/admin/login` - Admin authentication
   - `GET /api/admin/purim-votes-tallied` - Get vote results (protected)

3. **General**
   - `POST /api/contact` - Submit contact/subscription form

## Database Schema

### Contest Entries
- id: Unique identifier
- title: Entry title
- desc: Description
- images: Array of image paths/URLs
- mediaType: Type of media (image/video)

### Votes
- entryId: ID of the voted entry
- timestamp: When the vote was cast
- (optional) userIdentifier: Some way to identify unique users

### Admin Users
- username: Admin username
- password: Hashed password
- role: User role

## Security Considerations
- Implement CORS with proper origin restrictions
- Use helmet.js or similar for security headers
- Sanitize all user inputs
- Rate limit API endpoints
- Implement proper error handling without exposing sensitive information
- Use environment variables for sensitive configuration

## Deployment Considerations
- Set up environment variables for development and production
- Configure MongoDB connection for different environments
- Implement proper error logging
- Set up proper build and deployment pipeline

## Additional Notes
- The application should maintain the same look and feel as the original
- Ensure all functionality works correctly, especially the voting system
- Optimize for performance and accessibility
- Include proper documentation for the codebase