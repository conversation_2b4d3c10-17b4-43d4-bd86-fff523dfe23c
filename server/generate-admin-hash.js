/**
 * Utility script to generate a bcrypt hash for the admin password
 * 
 * SECURITY NOTE:
 * This script combines the username and password to create a more secure hash,
 * but ONLY THE HASH is stored - not your actual username or password.
 * 
 * How this works:
 * 1. Username and password are combined temporarily
 * 2. A secure hash is generated from this combined value
 * 3. Only the hash is saved in your .env file 
 * 4. The original username/password values are never stored
 * 
 * Usage:
 * 1. Run: node generate-admin-hash.js yourUsername yourSecurePassword
 * 2. Copy the generated hash
 * 3. Add to your .env file as ADMIN_PASSWORD_HASH=<generated hash>
 */

const bcrypt = require('bcryptjs');

// Check if username and password were provided as command line arguments
if (process.argv.length < 4) {
  console.error('Please provide a username and password as command line arguments');
  console.error('Usage: node generate-admin-hash.js <username> <password>');
  process.exit(1);
}

const username = process.argv[2];
const password = process.argv[3];

// Combine username and password for better security
// Note: The actual username/password are NOT stored anywhere
const combinedValue = `${username}:${password}`;

// Generate a strong hash with cost factor 12
bcrypt.hash(combinedValue, 12).then(hash => {
  console.log('Generated password hash:');
  // amazonq-ignore-next-line
  console.log(hash);
  console.log('\nAdd this to your .env file as:');
  // amazonq-ignore-next-line
  console.log(`ADMIN_PASSWORD_HASH=${hash}`);
  console.log('\nThe hash is secure - it does NOT contain your actual password or username.');
  console.log('No one can reverse it to get your credentials.');
}).catch(err => {
  // amazonq-ignore-next-line
  console.error('Error generating hash:', err);
}); 