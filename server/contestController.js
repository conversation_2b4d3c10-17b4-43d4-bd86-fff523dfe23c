// Assuming Submission model is imported, e.g.:
// const Submission = require('../models/Submission'); // Adjust path as needed

exports.getSubmissions = async (req, res, next) => {
  try {
    // The frontend calls /submissions without contestId in path or category in query.
    // It expects all relevant submissions categorized into 'drawings' and 'videos'.
    let query = {}; // Base query to find submissions

    // Fetch all submissions. Using .lean() for plain JavaScript objects for performance.
    const allSubmissions = await Submission.find(query).sort({ createdAt: -1 }).lean();

    // Filter submissions into drawings and videos.
    // This assumes submissions have a 'mediaType' field (e.g., 'image/jpeg', 'video/mp4').
    // This logic is based on the original controller's attempt to filter by mediaType.
    const drawings = allSubmissions.filter(submission =>
        submission.mediaType && typeof submission.mediaType === 'string' && submission.mediaType.toLowerCase().startsWith('image')
    );

    const videos = allSubmissions.filter(submission =>
        submission.mediaType && typeof submission.mediaType === 'string' && submission.mediaType.toLowerCase().startsWith('video')
    );

    // The frontend (contest.js) expects the response to be an object:
    // { submittedArtwork: { drawings: [...], videos: [...] } }
    res.json({
      submittedArtwork: {
        drawings: drawings,
        videos: videos
      }
    });

  } catch (err) {
    // Log the error on the server for debugging
    console.error('Error in getSubmissions controller:', err);

    // Send an error response. The frontend's .catch() block in contest.js
    // should ideally handle this and use its fallback data.
    res.status(500).json({
      submittedArtwork: { // Provide empty arrays in the expected structure as a fallback
        drawings: [],
        videos: []
      },
      error: 'Failed to retrieve submissions from the server.'
    });
  }
};

// If there were other exports in the original file, they are not included here.
// This content is intended to define or replace the getSubmissions export.

