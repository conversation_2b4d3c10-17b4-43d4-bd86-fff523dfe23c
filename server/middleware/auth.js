const jwt = require('jsonwebtoken');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware to verify admin JWT token
const isAdmin = (req, res, next) => {
  // Debug logging
  console.log('Cookies received:', req.cookies);
  console.log('Headers:', req.headers.cookie);

  // Ensure cookies object exists before trying to access adminToken
  const token = req.cookies && req.cookies.adminToken;

  if (!token) {
    console.log('No adminToken cookie found');
    return res.status(401).json({ success: false, message: 'No authentication token provided' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.admin = decoded;
    console.log('Token verified successfully for:', decoded.username);
    next();
  } catch (err) {
    // Log specific error details for server-side debugging
    console.error(`Token verification failed: ${err.name} - ${err.message}`);
    // Return a generic error message to the client for security
    return res.status(401).json({ success: false, message: 'Invalid or expired token' });
  }
};

// Export the middleware
module.exports = {
  isAdmin
};
