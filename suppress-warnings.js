// Suppress specific deprecation warnings
process.env.NODE_NO_WARNINGS = 1;

// Alternative approach - only hide this specific warning
const originalEmit = process.emit;
process.emit = function (name, data, ...args) {
    if (
        name === 'warning' &&
        data &&
        data.name === 'DeprecationWarning' &&
        data.code === 'DEP0060'
    ) {
        return false;
    }
    return originalEmit.apply(process, [name, data, ...args]);
}; 