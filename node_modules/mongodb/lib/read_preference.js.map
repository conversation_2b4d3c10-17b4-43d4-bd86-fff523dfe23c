{"version": 3, "file": "read_preference.js", "sourceRoot": "", "sources": ["../src/read_preference.ts"], "names": [], "mappings": ";;;AACA,mCAAoD;AAOpD,cAAc;AACD,QAAA,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC;IAC9C,OAAO,EAAE,SAAS;IAClB,gBAAgB,EAAE,kBAAkB;IACpC,SAAS,EAAE,WAAW;IACtB,kBAAkB,EAAE,oBAAoB;IACxC,OAAO,EAAE,SAAS;CACV,CAAC,CAAC;AAsCZ;;;;;;GAMG;AACH,MAAa,cAAc;IAmBzB;;;;OAIG;IACH,YAAY,IAAwB,EAAE,IAAe,EAAE,OAA+B;QACpF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACjC,MAAM,IAAI,iCAAyB,CAAC,gCAAgC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC7F;QACD,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvE,OAAO,GAAG,IAAI,CAAC;YACf,IAAI,GAAG,SAAS,CAAC;SAClB;aAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvC,MAAM,IAAI,iCAAyB,CAAC,sCAAsC,CAAC,CAAC;SAC7E;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,KAAK,CAAC;QAC5B,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAEhC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,OAAO,CAAC,mBAAmB,IAAI,IAAI,EAAE;YACvC,IAAI,OAAO,CAAC,mBAAmB,IAAI,CAAC,EAAE;gBACpC,MAAM,IAAI,iCAAyB,CAAC,gDAAgD,CAAC,CAAC;aACvF;YAED,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;YAEvD,yFAAyF;YACzF,6FAA6F;YAC7F,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;SACzB;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,CAAC,OAAO,EAAE;YACxC,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjE,MAAM,IAAI,iCAAyB,CAAC,sDAAsD,CAAC,CAAC;aAC7F;YAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,MAAM,IAAI,iCAAyB,CACjC,qEAAqE,CACtE,CAAC;aACH;YAED,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,MAAM,IAAI,iCAAyB,CACjC,uDAAuD,CACxD,CAAC;aACH;SACF;IACH,CAAC;IAED,iFAAiF;IACjF,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,IAAY;QAC5B,OAAO,IAAI,cAAc,CAAC,IAA0B,CAAC,CAAC;IACxD,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,WAAW,CAAC,OAAmC;QACpD,IAAI,CAAC,OAAO;YAAE,OAAO;QACrB,MAAM,cAAc,GAClB,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC;QAChF,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAEtD,IAAI,cAAc,IAAI,IAAI,EAAE;YAC1B,OAAO;SACR;QAED,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;YACtC,OAAO,IAAI,cAAc,CAAC,cAAc,EAAE,kBAAkB,EAAE;gBAC5D,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;gBAChD,KAAK,EAAE,OAAO,CAAC,KAAK;aACrB,CAAC,CAAC;SACJ;aAAM,IAAI,CAAC,CAAC,cAAc,YAAY,cAAc,CAAC,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;YAC5F,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,UAAU,CAAC;YAC9D,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBACpC,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,IAAI,IAAI,kBAAkB,EAAE;oBACzE,mBAAmB,EAAE,cAAc,CAAC,mBAAmB;oBACvD,KAAK,EAAE,OAAO,CAAC,KAAK;iBACrB,CAAC,CAAC;aACJ;SACF;QAED,IAAI,kBAAkB,EAAE;YACtB,cAAc,CAAC,IAAI,GAAG,kBAAkB,CAAC;SAC1C;QAED,OAAO,cAAgC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,OAAkC;QACjD,IAAI,OAAO,CAAC,cAAc,IAAI,IAAI;YAAE,OAAO,OAAO,CAAC;QACnD,MAAM,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC;QAEjC,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAO,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC;SAChD;aAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,cAAc,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACvE,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC;YACpC,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBACpC,OAAO,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE;oBACxD,mBAAmB,EAAE,CAAC,CAAC,mBAAmB;iBAC3C,CAAC,CAAC;aACJ;SACF;aAAM,IAAI,CAAC,CAAC,CAAC,YAAY,cAAc,CAAC,EAAE;YACzC,MAAM,IAAI,iCAAyB,CAAC,4BAA4B,CAAC,EAAE,CAAC,CAAC;SACtE;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,IAAY;QACzB,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC;YAC1B,cAAc,CAAC,OAAO;YACtB,cAAc,CAAC,iBAAiB;YAChC,cAAc,CAAC,SAAS;YACxB,cAAc,CAAC,mBAAmB;YAClC,cAAc,CAAC,OAAO;YACtB,IAAI;SACL,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC,GAAG,CAAC,IAA0B,CAAC,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,IAAa;QACnB,OAAO,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACH,WAAW;QACT,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAS;YACxC,cAAc,CAAC,iBAAiB;YAChC,cAAc,CAAC,SAAS;YACxB,cAAc,CAAC,mBAAmB;YAClC,cAAc,CAAC,OAAO;SACvB,CAAC,CAAC;QAEH,OAAO,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,cAA8B;QACnC,OAAO,cAAc,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;IAC3C,CAAC;IAED,iCAAiC;IACjC,MAAM;QACJ,MAAM,cAAc,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAc,CAAC;QACvD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC9D,IAAI,IAAI,CAAC,mBAAmB;YAAE,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC5F,IAAI,IAAI,CAAC,KAAK;YAAE,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAClD,OAAO,cAAc,CAAC;IACxB,CAAC;;AAjMa,sBAAO,GAAG,0BAAkB,CAAC,OAAO,CAAC;AACrC,gCAAiB,GAAG,0BAAkB,CAAC,gBAAgB,CAAC;AACxD,wBAAS,GAAG,0BAAkB,CAAC,SAAS,CAAC;AACzC,kCAAmB,GAAG,0BAAkB,CAAC,kBAAkB,CAAC;AAC5D,sBAAO,GAAG,0BAAkB,CAAC,OAAO,CAAC;AAErC,sBAAO,GAAG,IAAI,cAAc,CAAC,0BAAkB,CAAC,OAAO,CAAC,CAAC;AACzD,+BAAgB,GAAG,IAAI,cAAc,CAAC,0BAAkB,CAAC,gBAAgB,CAAC,CAAC;AAC3E,wBAAS,GAAG,IAAI,cAAc,CAAC,0BAAkB,CAAC,SAAS,CAAC,CAAC;AAC7D,iCAAkB,GAAG,IAAI,cAAc,CAAC,0BAAkB,CAAC,kBAAkB,CAAC,CAAC;AAC/E,sBAAO,GAAG,IAAI,cAAc,CAAC,0BAAkB,CAAC,OAAO,CAAC,CAAC;AAjB5D,wCAAc"}