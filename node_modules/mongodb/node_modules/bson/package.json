{"name": "bson", "description": "A bson parser for node.js and the browser", "keywords": ["mongodb", "bson", "parser"], "files": ["lib", "src", "bson.d.ts", "etc/prepare.js", "vendor"], "types": "bson.d.ts", "version": "5.5.1", "author": {"name": "The MongoDB NodeJS Team", "email": "<EMAIL>"}, "license": "Apache-2.0", "contributors": [], "repository": "mongodb/js-bson", "bugs": {"url": "https://jira.mongodb.org/projects/NODE/issues/"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@microsoft/api-extractor": "^7.35.1", "@octokit/core": "^4.2.4", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-typescript": "^11.1.0", "@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "@types/node": "^18.16.3", "@types/sinon": "^10.0.14", "@types/sinon-chai": "^3.2.9", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "benchmark": "^2.1.4", "chai": "^4.3.7", "chalk": "^5.2.0", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-no-bigint-usage": "file:./etc/eslint/no-bigint-usage", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-tsdoc": "^0.2.17", "magic-string": "^0.30.0", "mocha": "10.2.0", "node-fetch": "^3.3.1", "nyc": "^15.1.0", "prettier": "^2.8.8", "rollup": "^3.21.4", "sinon": "^15.0.4", "sinon-chai": "^3.7.0", "source-map-support": "^0.5.21", "standard-version": "^9.5.0", "ts-node": "^10.9.1", "tsd": "^0.28.1", "typescript": "^5.0.4", "typescript-cached-transpile": "0.0.6", "uuid": "^9.0.0", "v8-profiler-next": "^1.9.0"}, "tsd": {"directory": "test/types", "compilerOptions": {"strict": true, "target": "esnext", "module": "commonjs", "moduleResolution": "node"}}, "config": {"native": false}, "main": "./lib/bson.cjs", "module": "./lib/bson.mjs", "exports": {"import": {"types": "./bson.d.ts", "default": "./lib/bson.mjs"}, "require": {"types": "./bson.d.ts", "default": "./lib/bson.cjs"}, "react-native": "./lib/bson.rn.cjs", "browser": "./lib/bson.mjs"}, "compass:exports": {"import": "./lib/bson.cjs", "require": "./lib/bson.cjs"}, "engines": {"node": ">=14.20.1"}, "scripts": {"pretest": "npm run build", "test": "npm run check:node && npm run check:web && npm run check:web-no-bigint", "check:node": "WEB=false mocha test/node", "check:tsd": "npm run build:dts && tsd", "check:web": "WEB=true mocha test/node", "check:web-no-bigint": "WEB=true NO_BIGINT=true mocha test/node", "build:ts": "node ./node_modules/typescript/bin/tsc", "build:dts": "npm run build:ts && api-extractor run --typescript-compiler-folder node_modules/typescript --local && node etc/clean_definition_files.cjs", "build:bundle": "rollup -c rollup.config.mjs", "build": "npm run build:dts && npm run build:bundle", "check:lint": "eslint -v && eslint --ext '.js,.ts' --max-warnings=0 src test && npm run build:dts && npm run check:tsd", "format": "eslint --ext '.js,.ts' src test --fix", "check:coverage": "nyc --check-coverage npm run check:node", "prepare": "node etc/prepare.js", "release": "standard-version -i HISTORY.md"}}