{"version": 3, "file": "executor.js", "sourceRoot": "", "sources": ["../../../src/operations/client_bulk_write/executor.ts"], "names": [], "mappings": ";;;AACA,kEAAuF;AACvF,oFAA8E;AAC9E,uCAKqB;AAErB,2CAA+C;AAC/C,uCAAoD;AACpD,uDAAmD;AACnD,4DAAwD;AACxD,2DAA+D;AAC/D,uDAAkE;AAMlE,qDAAgE;AAEhE;;;GAGG;AACH,MAAa,uBAAuB;IAKlC;;;;;OAKG;IACH,YACE,MAAmB,EACnB,UAA4D,EAC5D,OAAgC;QAEhC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,0CAAkC,CAAC,4CAA4C,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,IAAI;YACb,wBAAwB,EAAE,KAAK;YAC/B,cAAc,EAAE,KAAK;YACrB,GAAG,OAAO;SACX,CAAC;QAEF,oEAAoE;QACpE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,4BAAY,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAChC,MAAM,IAAI,iCAAyB,CACjC,iEAAiE,CAClE,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzB,MAAM,IAAI,iCAAyB,CACjC,gEAAgE,CACjE,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO;QACX,uFAAuF;QACvF,sCAAsC;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;QAClD,MAAM,cAAc,GAAG,IAAI,+CAA6B,CACtD,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,OAAO,EACZ,SAAS,CACV,CAAC;QACF,wEAAwE;QACxE,MAAM,eAAe,GAAG,IAAA,6BAAqB,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,wBAAc,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAEvD,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO,cAAc,CAAC,YAAY,EAAE,EAAE,CAAC;gBACrC,MAAM,SAAS,GAAG,IAAI,4CAAwB,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC7E,MAAM,IAAA,oCAAgB,EAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YAC1D,CAAC;YACD,OAAO,6CAA4B,CAAC,cAAc,EAAE,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,MAAM,aAAa,GAAG,IAAI,6CAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrE,0EAA0E;YAC1E,OAAO,cAAc,CAAC,YAAY,EAAE,EAAE,CAAC;gBACrC,MAAM,aAAa,GAAG,IAAI,sCAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;gBAClE,MAAM,OAAO,GAAG;oBACd,GAAG,IAAI,CAAC,OAAO;oBACf,cAAc,EAAE,aAAa;oBAC7B,GAAG,CAAC,eAAe,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,WAAW,EAAE,mCAAiB,CAAC,QAAQ,EAAE,CAAC;iBACtF,CAAC;gBACF,MAAM,MAAM,GAAG,IAAI,gDAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;gBAC/E,IAAI,CAAC;oBACH,MAAM,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACpC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,kGAAkG;oBAClG,iGAAiG;oBACjG,mGAAmG;oBACnG,0DAA0D;oBAC1D,IAAI,KAAK,YAAY,wBAAgB,IAAI,CAAC,CAAC,KAAK,YAAY,iCAAyB,CAAC,EAAE,CAAC;wBACvF,2FAA2F;wBAC3F,sEAAsE;wBACtE,MAAM,cAAc,GAAG,IAAI,iCAAyB,CAAC;4BACnD,OAAO,EAAE,+DAA+D;yBACzE,CAAC,CAAC;wBACH,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;wBAC7B,cAAc,CAAC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC;wBAC7D,MAAM,cAAc,CAAC;oBACvB,CAAC;yBAAM,CAAC;wBACN,sCAAsC;wBACtC,MAAM,KAAK,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;YAED,iFAAiF;YACjF,IAAI,aAAa,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAAI,aAAa,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBACtF,MAAM,KAAK,GAAG,IAAI,iCAAyB,CAAC;oBAC1C,OAAO,EAAE,8DAA8D;iBACxE,CAAC,CAAC;gBACH,KAAK,CAAC,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,CAAC;gBAC5D,KAAK,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;gBAC9C,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC;gBACpD,MAAM,KAAK,CAAC;YACd,CAAC;YAED,OAAO,aAAa,CAAC,eAAe,CAAC;QACvC,CAAC;IACH,CAAC;CACF;AAzHD,0DAyHC"}